{"version": 3, "sources": ["../../@emotion/styled/base/dist/emotion-styled-base.browser.development.esm.js", "../../@emotion/styled/dist/emotion-styled.browser.development.esm.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { withEmotionCache, ThemeContext } from '@emotion/react';\nimport { serializeStyles } from '@emotion/serialize';\nimport { useInsertionEffectAlwaysWithSyncFallback } from '@emotion/use-insertion-effect-with-fallbacks';\nimport { getRegisteredStyles, registerStyles, insertStyles } from '@emotion/utils';\nimport * as React from 'react';\nimport isPropValid from '@emotion/is-prop-valid';\n\nvar isDevelopment = true;\n\nvar testOmitPropsOnStringTag = isPropValid;\n\nvar testOmitPropsOnComponent = function testOmitPropsOnComponent(key) {\n  return key !== 'theme';\n};\n\nvar getDefaultShouldForwardProp = function getDefaultShouldForwardProp(tag) {\n  return typeof tag === 'string' && // 96 is one less than the char code\n  // for \"a\" so this is checking that\n  // it's a lowercase character\n  tag.charCodeAt(0) > 96 ? testOmitPropsOnStringTag : testOmitPropsOnComponent;\n};\nvar composeShouldForwardProps = function composeShouldForwardProps(tag, options, isReal) {\n  var shouldForwardProp;\n\n  if (options) {\n    var optionsShouldForwardProp = options.shouldForwardProp;\n    shouldForwardProp = tag.__emotion_forwardProp && optionsShouldForwardProp ? function (propName) {\n      return tag.__emotion_forwardProp(propName) && optionsShouldForwardProp(propName);\n    } : optionsShouldForwardProp;\n  }\n\n  if (typeof shouldForwardProp !== 'function' && isReal) {\n    shouldForwardProp = tag.__emotion_forwardProp;\n  }\n\n  return shouldForwardProp;\n};\n\nvar ILLEGAL_ESCAPE_SEQUENCE_ERROR = \"You have illegal escape sequence in your template literal, most likely inside content's property value.\\nBecause you write your CSS inside a JavaScript string you actually have to do double escaping, so for example \\\"content: '\\\\00d7';\\\" should become \\\"content: '\\\\\\\\00d7';\\\".\\nYou can read more about this here:\\nhttps://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals#ES2018_revision_of_illegal_escape_sequences\";\n\nvar Insertion = function Insertion(_ref) {\n  var cache = _ref.cache,\n      serialized = _ref.serialized,\n      isStringTag = _ref.isStringTag;\n  registerStyles(cache, serialized, isStringTag);\n  useInsertionEffectAlwaysWithSyncFallback(function () {\n    return insertStyles(cache, serialized, isStringTag);\n  });\n\n  return null;\n};\n\nvar createStyled = function createStyled(tag, options) {\n  {\n    if (tag === undefined) {\n      throw new Error('You are trying to create a styled element with an undefined component.\\nYou may have forgotten to import it.');\n    }\n  }\n\n  var isReal = tag.__emotion_real === tag;\n  var baseTag = isReal && tag.__emotion_base || tag;\n  var identifierName;\n  var targetClassName;\n\n  if (options !== undefined) {\n    identifierName = options.label;\n    targetClassName = options.target;\n  }\n\n  var shouldForwardProp = composeShouldForwardProps(tag, options, isReal);\n  var defaultShouldForwardProp = shouldForwardProp || getDefaultShouldForwardProp(baseTag);\n  var shouldUseAs = !defaultShouldForwardProp('as');\n  return function () {\n    // eslint-disable-next-line prefer-rest-params\n    var args = arguments;\n    var styles = isReal && tag.__emotion_styles !== undefined ? tag.__emotion_styles.slice(0) : [];\n\n    if (identifierName !== undefined) {\n      styles.push(\"label:\" + identifierName + \";\");\n    }\n\n    if (args[0] == null || args[0].raw === undefined) {\n      // eslint-disable-next-line prefer-spread\n      styles.push.apply(styles, args);\n    } else {\n      var templateStringsArr = args[0];\n\n      if (templateStringsArr[0] === undefined) {\n        console.error(ILLEGAL_ESCAPE_SEQUENCE_ERROR);\n      }\n\n      styles.push(templateStringsArr[0]);\n      var len = args.length;\n      var i = 1;\n\n      for (; i < len; i++) {\n        if (templateStringsArr[i] === undefined) {\n          console.error(ILLEGAL_ESCAPE_SEQUENCE_ERROR);\n        }\n\n        styles.push(args[i], templateStringsArr[i]);\n      }\n    }\n\n    var Styled = withEmotionCache(function (props, cache, ref) {\n      var FinalTag = shouldUseAs && props.as || baseTag;\n      var className = '';\n      var classInterpolations = [];\n      var mergedProps = props;\n\n      if (props.theme == null) {\n        mergedProps = {};\n\n        for (var key in props) {\n          mergedProps[key] = props[key];\n        }\n\n        mergedProps.theme = React.useContext(ThemeContext);\n      }\n\n      if (typeof props.className === 'string') {\n        className = getRegisteredStyles(cache.registered, classInterpolations, props.className);\n      } else if (props.className != null) {\n        className = props.className + \" \";\n      }\n\n      var serialized = serializeStyles(styles.concat(classInterpolations), cache.registered, mergedProps);\n      className += cache.key + \"-\" + serialized.name;\n\n      if (targetClassName !== undefined) {\n        className += \" \" + targetClassName;\n      }\n\n      var finalShouldForwardProp = shouldUseAs && shouldForwardProp === undefined ? getDefaultShouldForwardProp(FinalTag) : defaultShouldForwardProp;\n      var newProps = {};\n\n      for (var _key in props) {\n        if (shouldUseAs && _key === 'as') continue;\n\n        if (finalShouldForwardProp(_key)) {\n          newProps[_key] = props[_key];\n        }\n      }\n\n      newProps.className = className;\n\n      if (ref) {\n        newProps.ref = ref;\n      }\n\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Insertion, {\n        cache: cache,\n        serialized: serialized,\n        isStringTag: typeof FinalTag === 'string'\n      }), /*#__PURE__*/React.createElement(FinalTag, newProps));\n    });\n    Styled.displayName = identifierName !== undefined ? identifierName : \"Styled(\" + (typeof baseTag === 'string' ? baseTag : baseTag.displayName || baseTag.name || 'Component') + \")\";\n    Styled.defaultProps = tag.defaultProps;\n    Styled.__emotion_real = Styled;\n    Styled.__emotion_base = baseTag;\n    Styled.__emotion_styles = styles;\n    Styled.__emotion_forwardProp = shouldForwardProp;\n    Object.defineProperty(Styled, 'toString', {\n      value: function value() {\n        if (targetClassName === undefined && isDevelopment) {\n          return 'NO_COMPONENT_SELECTOR';\n        }\n\n        return \".\" + targetClassName;\n      }\n    });\n\n    Styled.withComponent = function (nextTag, nextOptions) {\n      var newStyled = createStyled(nextTag, _extends({}, options, nextOptions, {\n        shouldForwardProp: composeShouldForwardProps(Styled, nextOptions, true)\n      }));\n      return newStyled.apply(void 0, styles);\n    };\n\n    return Styled;\n  };\n};\n\nexport { createStyled as default };\n", "import createStyled from '../base/dist/emotion-styled-base.browser.development.esm.js';\nimport '@babel/runtime/helpers/extends';\nimport '@emotion/react';\nimport '@emotion/serialize';\nimport '@emotion/use-insertion-effect-with-fallbacks';\nimport '@emotion/utils';\nimport 'react';\nimport '@emotion/is-prop-valid';\n\nvar tags = ['a', 'abbr', 'address', 'area', 'article', 'aside', 'audio', 'b', 'base', 'bdi', 'bdo', 'big', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'cite', 'code', 'col', 'colgroup', 'data', 'datalist', 'dd', 'del', 'details', 'dfn', 'dialog', 'div', 'dl', 'dt', 'em', 'embed', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'iframe', 'img', 'input', 'ins', 'kbd', 'keygen', 'label', 'legend', 'li', 'link', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meta', 'meter', 'nav', 'noscript', 'object', 'ol', 'optgroup', 'option', 'output', 'p', 'param', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'script', 'section', 'select', 'small', 'source', 'span', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'textarea', 'tfoot', 'th', 'thead', 'time', 'title', 'tr', 'track', 'u', 'ul', 'var', 'video', 'wbr', // SVG\n'circle', 'clipPath', 'defs', 'ellipse', 'foreignObject', 'g', 'image', 'line', 'linearGradient', 'mask', 'path', 'pattern', 'polygon', 'polyline', 'radialGradient', 'rect', 'stop', 'svg', 'text', 'tspan'];\n\n// bind it to avoid mutating the original function\nvar newStyled = createStyled.bind(null);\ntags.forEach(function (tagName) {\n  newStyled[tagName] = newStyled(tagName);\n});\n\nexport { newStyled as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKA,YAAuB;AACvB;AAEA,IAAI,gBAAgB;AAEpB,IAAI,2BAA2B;AAE/B,IAAI,2BAA2B,SAASA,0BAAyB,KAAK;AACpE,SAAO,QAAQ;AACjB;AAEA,IAAI,8BAA8B,SAASC,6BAA4B,KAAK;AAC1E,SAAO,OAAO,QAAQ;AAAA;AAAA;AAAA,EAGtB,IAAI,WAAW,CAAC,IAAI,KAAK,2BAA2B;AACtD;AACA,IAAI,4BAA4B,SAASC,2BAA0B,KAAK,SAAS,QAAQ;AACvF,MAAI;AAEJ,MAAI,SAAS;AACX,QAAI,2BAA2B,QAAQ;AACvC,wBAAoB,IAAI,yBAAyB,2BAA2B,SAAU,UAAU;AAC9F,aAAO,IAAI,sBAAsB,QAAQ,KAAK,yBAAyB,QAAQ;AAAA,IACjF,IAAI;AAAA,EACN;AAEA,MAAI,OAAO,sBAAsB,cAAc,QAAQ;AACrD,wBAAoB,IAAI;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,IAAI,gCAAgC;AAAA;AAAA;AAAA;AAEpC,IAAI,YAAY,SAASC,WAAU,MAAM;AACvC,MAAI,QAAQ,KAAK,OACb,aAAa,KAAK,YAClB,cAAc,KAAK;AACvB,iBAAe,OAAO,YAAY,WAAW;AAC7C,2CAAyC,WAAY;AACnD,WAAO,aAAa,OAAO,YAAY,WAAW;AAAA,EACpD,CAAC;AAED,SAAO;AACT;AAEA,IAAI,eAAe,SAASC,cAAa,KAAK,SAAS;AACrD;AACE,QAAI,QAAQ,QAAW;AACrB,YAAM,IAAI,MAAM,8GAA8G;AAAA,IAChI;AAAA,EACF;AAEA,MAAI,SAAS,IAAI,mBAAmB;AACpC,MAAI,UAAU,UAAU,IAAI,kBAAkB;AAC9C,MAAI;AACJ,MAAI;AAEJ,MAAI,YAAY,QAAW;AACzB,qBAAiB,QAAQ;AACzB,sBAAkB,QAAQ;AAAA,EAC5B;AAEA,MAAI,oBAAoB,0BAA0B,KAAK,SAAS,MAAM;AACtE,MAAI,2BAA2B,qBAAqB,4BAA4B,OAAO;AACvF,MAAI,cAAc,CAAC,yBAAyB,IAAI;AAChD,SAAO,WAAY;AAEjB,QAAI,OAAO;AACX,QAAI,SAAS,UAAU,IAAI,qBAAqB,SAAY,IAAI,iBAAiB,MAAM,CAAC,IAAI,CAAC;AAE7F,QAAI,mBAAmB,QAAW;AAChC,aAAO,KAAK,WAAW,iBAAiB,GAAG;AAAA,IAC7C;AAEA,QAAI,KAAK,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,QAAW;AAEhD,aAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,IAChC,OAAO;AACL,UAAI,qBAAqB,KAAK,CAAC;AAE/B,UAAI,mBAAmB,CAAC,MAAM,QAAW;AACvC,gBAAQ,MAAM,6BAA6B;AAAA,MAC7C;AAEA,aAAO,KAAK,mBAAmB,CAAC,CAAC;AACjC,UAAI,MAAM,KAAK;AACf,UAAI,IAAI;AAER,aAAO,IAAI,KAAK,KAAK;AACnB,YAAI,mBAAmB,CAAC,MAAM,QAAW;AACvC,kBAAQ,MAAM,6BAA6B;AAAA,QAC7C;AAEA,eAAO,KAAK,KAAK,CAAC,GAAG,mBAAmB,CAAC,CAAC;AAAA,MAC5C;AAAA,IACF;AAEA,QAAI,SAAS,iBAAiB,SAAU,OAAO,OAAO,KAAK;AACzD,UAAI,WAAW,eAAe,MAAM,MAAM;AAC1C,UAAI,YAAY;AAChB,UAAI,sBAAsB,CAAC;AAC3B,UAAI,cAAc;AAElB,UAAI,MAAM,SAAS,MAAM;AACvB,sBAAc,CAAC;AAEf,iBAAS,OAAO,OAAO;AACrB,sBAAY,GAAG,IAAI,MAAM,GAAG;AAAA,QAC9B;AAEA,oBAAY,QAAc,iBAAW,YAAY;AAAA,MACnD;AAEA,UAAI,OAAO,MAAM,cAAc,UAAU;AACvC,oBAAY,oBAAoB,MAAM,YAAY,qBAAqB,MAAM,SAAS;AAAA,MACxF,WAAW,MAAM,aAAa,MAAM;AAClC,oBAAY,MAAM,YAAY;AAAA,MAChC;AAEA,UAAI,aAAa,gBAAgB,OAAO,OAAO,mBAAmB,GAAG,MAAM,YAAY,WAAW;AAClG,mBAAa,MAAM,MAAM,MAAM,WAAW;AAE1C,UAAI,oBAAoB,QAAW;AACjC,qBAAa,MAAM;AAAA,MACrB;AAEA,UAAI,yBAAyB,eAAe,sBAAsB,SAAY,4BAA4B,QAAQ,IAAI;AACtH,UAAI,WAAW,CAAC;AAEhB,eAAS,QAAQ,OAAO;AACtB,YAAI,eAAe,SAAS,KAAM;AAElC,YAAI,uBAAuB,IAAI,GAAG;AAChC,mBAAS,IAAI,IAAI,MAAM,IAAI;AAAA,QAC7B;AAAA,MACF;AAEA,eAAS,YAAY;AAErB,UAAI,KAAK;AACP,iBAAS,MAAM;AAAA,MACjB;AAEA,aAA0B,oBAAoB,gBAAU,MAAyB,oBAAc,WAAW;AAAA,QACxG;AAAA,QACA;AAAA,QACA,aAAa,OAAO,aAAa;AAAA,MACnC,CAAC,GAAsB,oBAAc,UAAU,QAAQ,CAAC;AAAA,IAC1D,CAAC;AACD,WAAO,cAAc,mBAAmB,SAAY,iBAAiB,aAAa,OAAO,YAAY,WAAW,UAAU,QAAQ,eAAe,QAAQ,QAAQ,eAAe;AAChL,WAAO,eAAe,IAAI;AAC1B,WAAO,iBAAiB;AACxB,WAAO,iBAAiB;AACxB,WAAO,mBAAmB;AAC1B,WAAO,wBAAwB;AAC/B,WAAO,eAAe,QAAQ,YAAY;AAAA,MACxC,OAAO,SAAS,QAAQ;AACtB,YAAI,oBAAoB,UAAa,eAAe;AAClD,iBAAO;AAAA,QACT;AAEA,eAAO,MAAM;AAAA,MACf;AAAA,IACF,CAAC;AAED,WAAO,gBAAgB,SAAU,SAAS,aAAa;AACrD,UAAIC,aAAYD,cAAa,SAAS,SAAS,CAAC,GAAG,SAAS,aAAa;AAAA,QACvE,mBAAmB,0BAA0B,QAAQ,aAAa,IAAI;AAAA,MACxE,CAAC,CAAC;AACF,aAAOC,WAAU,MAAM,QAAQ,MAAM;AAAA,IACvC;AAEA,WAAO;AAAA,EACT;AACF;;;AChLA,IAAAC,gBAAO;AACP;AAEA,IAAI,OAAO;AAAA,EAAC;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAM;AAAA,EAAO;AAAA,EAAW;AAAA,EAAO;AAAA,EAAU;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAS;AAAA,EAAY;AAAA,EAAc;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAK;AAAA,EAAU;AAAA,EAAO;AAAA,EAAS;AAAA,EAAO;AAAA,EAAO;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAAY;AAAA,EAAU;AAAA,EAAM;AAAA,EAAY;AAAA,EAAU;AAAA,EAAU;AAAA,EAAK;AAAA,EAAS;AAAA,EAAW;AAAA,EAAO;AAAA,EAAY;AAAA,EAAK;AAAA,EAAM;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAW;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAO;AAAA,EAAW;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAM;AAAA,EAAY;AAAA,EAAS;AAAA,EAAM;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAM;AAAA,EAAS;AAAA,EAAK;AAAA,EAAM;AAAA,EAAO;AAAA,EAAS;AAAA;AAAA,EAC77B;AAAA,EAAU;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAiB;AAAA,EAAK;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAO;AAG5M,IAAI,YAAY,aAAa,KAAK,IAAI;AACtC,KAAK,QAAQ,SAAU,SAAS;AAC9B,YAAU,OAAO,IAAI,UAAU,OAAO;AACxC,CAAC;", "names": ["testOmitPropsOnComponent", "getDefaultShouldForwardProp", "composeShouldForwardProps", "Insertion", "createStyled", "newStyled", "import_react"]}