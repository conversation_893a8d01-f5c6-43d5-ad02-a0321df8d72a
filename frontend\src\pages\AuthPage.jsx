import React, { useState, useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Box,
  Container,
  useTheme,
  useMediaQuery,
  Fade,
  Zoom
} from '@mui/material'
import { useAuth } from '../context/AuthContext'
import { useSnackbar } from 'notistack'
import confetti from 'canvas-confetti'

// Import new auth components
import AuthBackground from '../components/auth/AuthBackground'
import LoginForm from '../components/auth/LoginForm'
import RegisterForm from '../components/auth/RegisterForm'
import AuthToggle from '../components/auth/AuthToggle'
import SocialLogin from '../components/auth/SocialLogin'

const AuthPage = () => {
  const [isLogin, setIsLogin] = useState(true)
  const [showSuccess, setShowSuccess] = useState(false)
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))

  const { isAuthenticated, loading, error, clearErrors } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  useEffect(() => {
    if (error) {
      enqueueSnackbar(error, { variant: 'error' })
      clearErrors()
    }
  }, [error, clearErrors, enqueueSnackbar])

  const handleAuthSuccess = () => {
    setShowSuccess(true)
    // Trigger confetti animation
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 }
    })

    setTimeout(() => {
      setShowSuccess(false)
    }, 2000)
  }

  const toggleAuthMode = () => {
    setIsLogin(!isLogin)
    clearErrors()
  }

  if (isAuthenticated && !showSuccess) {
    return <Navigate to="/" />
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        position: 'relative',
        overflow: 'hidden',
        background: theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'
          : 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
      }}
    >
      {/* Animated Background */}
      <AuthBackground />

      {/* Main Content */}
      <Container
        maxWidth="sm"
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          zIndex: 2,
          py: 4
        }}
      >
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          style={{ width: '100%', maxWidth: '480px' }}
        >
          <Box
            sx={{
              background: theme.palette.mode === 'dark'
                ? 'rgba(30, 30, 30, 0.95)'
                : 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              boxShadow: theme.palette.mode === 'dark'
                ? '0 25px 50px rgba(0, 0, 0, 0.5)'
                : '0 25px 50px rgba(0, 0, 0, 0.15)',
              border: `1px solid ${theme.palette.mode === 'dark'
                ? 'rgba(255, 255, 255, 0.1)'
                : 'rgba(255, 255, 255, 0.2)'}`,
              overflow: 'hidden',
              position: 'relative'
            }}
          >
            {/* Header with Logo and Branding */}
            <Box
              sx={{
                textAlign: 'center',
                p: 4,
                pb: 2,
                background: theme.palette.mode === 'dark'
                  ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'
                  : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',
              }}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    mx: 'auto',
                    mb: 2,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
                    fontSize: '2rem'
                  }}
                >
                  💬
                </Box>
                <motion.h1
                  style={{
                    fontSize: '2.2rem',
                    fontWeight: 'bold',
                    margin: '0 0 0.5rem 0',
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(45deg, #fff 30%, #e0e0e0 90%)'
                      : 'linear-gradient(45deg, #333 30%, #666 90%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}
                >
                  Let's Talk
                </motion.h1>
                <motion.p
                  style={{
                    fontSize: '1rem',
                    color: theme.palette.text.secondary,
                    margin: 0,
                    lineHeight: 1.5
                  }}
                >
                  Connect, Share, and Discover Amazing Conversations
                </motion.p>
              </motion.div>
            </Box>

            {/* Auth Forms Container */}
            <Box
              sx={{
                p: 4,
                pt: 2
              }}
            >
              {/* Auth Toggle */}
              <AuthToggle isLogin={isLogin} onToggle={toggleAuthMode} />

              {/* Social Login */}
              <SocialLogin />

              {/* Divider */}
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  my: 3,
                  '&::before, &::after': {
                    content: '""',
                    flex: 1,
                    height: '1px',
                    background: theme.palette.divider
                  },
                  '&::before': { mr: 2 },
                  '&::after': { ml: 2 }
                }}
              >
                <Box
                  sx={{
                    px: 2,
                    color: theme.palette.text.secondary,
                    fontSize: '0.875rem',
                    fontWeight: 500
                  }}
                >
                  or continue with email
                </Box>
              </Box>

              {/* Auth Forms */}
              <AnimatePresence mode="wait">
                {isLogin ? (
                  <motion.div
                    key="login"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.3 }}
                  >
                    <LoginForm onSuccess={handleAuthSuccess} />
                  </motion.div>
                ) : (
                  <motion.div
                    key="register"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.3 }}
                  >
                    <RegisterForm onSuccess={handleAuthSuccess} />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Footer Features */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Box
                  sx={{
                    mt: 4,
                    pt: 3,
                    borderTop: `1px solid ${theme.palette.divider}`,
                    textAlign: 'center'
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      gap: 3,
                      flexWrap: 'wrap',
                      mb: 2
                    }}
                  >
                    {[
                      { icon: '🚀', text: 'Modern UI' },
                      { icon: '💬', text: 'Real-time Chat' },
                      { icon: '🔒', text: 'Secure' },
                      { icon: '🎨', text: 'Customizable' }
                    ].map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.7 + index * 0.1 }}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          fontSize: '0.875rem',
                          color: theme.palette.text.secondary
                        }}
                      >
                        <span style={{ fontSize: '1.2rem' }}>{feature.icon}</span>
                        {feature.text}
                      </motion.div>
                    ))}
                  </Box>

                  <Box
                    sx={{
                      fontSize: '0.75rem',
                      color: theme.palette.text.secondary,
                      opacity: 0.7
                    }}
                  >
                    By continuing, you agree to our{' '}
                    <Box
                      component="span"
                      sx={{
                        color: theme.palette.primary.main,
                        cursor: 'pointer',
                        '&:hover': { textDecoration: 'underline' }
                      }}
                    >
                      Terms of Service
                    </Box>
                    {' '}and{' '}
                    <Box
                      component="span"
                      sx={{
                        color: theme.palette.primary.main,
                        cursor: 'pointer',
                        '&:hover': { textDecoration: 'underline' }
                      }}
                    >
                      Privacy Policy
                    </Box>
                  </Box>
                </Box>
              </motion.div>
            </Box>
          </Box>
        </motion.div>
      </Container>

      {/* Success Overlay */}
      <AnimatePresence>
        {showSuccess && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'rgba(0, 0, 0, 0.8)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 9999
            }}
          >
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              style={{
                background: 'white',
                borderRadius: '20px',
                padding: '3rem',
                textAlign: 'center',
                boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)'
              }}
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                style={{ fontSize: '4rem', marginBottom: '1rem' }}
              >
                🎉
              </motion.div>
              <h2 style={{ margin: '0 0 1rem 0', color: '#333' }}>
                Welcome to Let's Talk!
              </h2>
              <p style={{ margin: 0, color: '#666' }}>
                Redirecting you to your dashboard...
              </p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </Box>
  )
}

export default AuthPage
