import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Box,
  TextField,
  Button,
  Typography,
  InputAdornment,
  IconButton,
  Checkbox,
  FormControlLabel,
  Link,
  Divider,
  useTheme
} from '@mui/material'
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  Login as LoginIcon
} from '@mui/icons-material'
import { useAuthForm } from '../../hooks/useAuthForm'
import { loginSchema } from '../../utils/validation'

const LoginForm = ({ onSuccess }) => {
  const theme = useTheme()
  const [showForgotPassword, setShowForgotPassword] = useState(false)

  const {
    formik,
    isSubmitting,
    showPassword,
    rememberMe,
    togglePasswordVisibility,
    handleRememberMeChange
  } = useAuthForm(
    {
      email: '',
      password: ''
    },
    loginSchema,
    onSuccess
  )

  const handleDemoLogin = () => {
    formik.setValues({
      email: '<EMAIL>',
      password: 'demo123'
    })
    // Auto-submit after a short delay to show the values being filled
    setTimeout(() => {
      formik.handleSubmit()
    }, 500)
  }

  const handleForgotPassword = () => {
    setShowForgotPassword(true)
    // TODO: Implement forgot password modal/flow
    console.log('Forgot password clicked')
  }

  return (
    <Box component="form" onSubmit={formik.handleSubmit} noValidate>
      {/* Email/Username Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <TextField
          fullWidth
          name="email"
          label="Email or Username"
          type="text"
          value={formik.values.email}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.email && Boolean(formik.errors.email)}
          helperText={formik.touched.email && formik.errors.email}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Email sx={{ color: theme.palette.text.secondary }} />
              </InputAdornment>
            )
          }}
          sx={{
            mb: 3,
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}20`
              },
              '&.Mui-focused': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}30`
              }
            }
          }}
        />
      </motion.div>

      {/* Password Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <TextField
          fullWidth
          name="password"
          label="Password"
          type={showPassword ? 'text' : 'password'}
          value={formik.values.password}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.password && Boolean(formik.errors.password)}
          helperText={formik.touched.password && formik.errors.password}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Lock sx={{ color: theme.palette.text.secondary }} />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={togglePasswordVisibility}
                  edge="end"
                  sx={{ color: theme.palette.text.secondary }}
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            )
          }}
          sx={{
            mb: 2,
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}20`
              },
              '&.Mui-focused': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}30`
              }
            }
          }}
        />
      </motion.div>

      {/* Remember Me & Forgot Password */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3
          }}
        >
          <FormControlLabel
            control={
              <Checkbox
                checked={rememberMe}
                onChange={handleRememberMeChange}
                sx={{
                  color: theme.palette.primary.main,
                  '&.Mui-checked': {
                    color: theme.palette.primary.main
                  }
                }}
              />
            }
            label={
              <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                Remember me
              </Typography>
            }
          />

          <Link
            component="button"
            type="button"
            onClick={handleForgotPassword}
            sx={{
              fontSize: '0.875rem',
              color: theme.palette.primary.main,
              textDecoration: 'none',
              fontWeight: 500,
              '&:hover': {
                textDecoration: 'underline'
              }
            }}
          >
            Forgot password?
          </Link>
        </Box>
      </motion.div>

      {/* Submit Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Button
          type="submit"
          fullWidth
          variant="contained"
          size="large"
          disabled={isSubmitting}
          startIcon={!isSubmitting && <LoginIcon />}
          sx={{
            py: 1.5,
            borderRadius: 2,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            fontWeight: 600,
            fontSize: '1rem',
            textTransform: 'none',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              transform: 'translateY(-2px)',
              boxShadow: '0 6px 16px rgba(102, 126, 234, 0.5)'
            },
            '&:disabled': {
              background: theme.palette.action.disabledBackground,
              color: theme.palette.action.disabled
            },
            transition: 'all 0.2s ease'
          }}
        >
          {isSubmitting ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              style={{
                width: 20,
                height: 20,
                border: '2px solid transparent',
                borderTop: '2px solid currentColor',
                borderRadius: '50%'
              }}
            />
          ) : (
            'Sign In'
          )}
        </Button>
      </motion.div>

      {/* Additional Login Options */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Box sx={{ mt: 3 }}>
          {/* Quick Access Features */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2,
              p: 2,
              backgroundColor: theme.palette.mode === 'dark'
                ? 'rgba(255, 255, 255, 0.05)'
                : 'rgba(0, 0, 0, 0.02)',
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: theme.palette.success.main,
                  animation: 'pulse 2s infinite'
                }}
              />
              <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                Secure Login
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ fontSize: '0.75rem', opacity: 0.7 }}>
              SSL Encrypted
            </Typography>
          </Box>

          {/* Demo Account Option */}
          <Box
            onClick={handleDemoLogin}
            sx={{
              textAlign: 'center',
              p: 2,
              backgroundColor: theme.palette.mode === 'dark'
                ? 'rgba(102, 126, 234, 0.1)'
                : 'rgba(102, 126, 234, 0.05)',
              borderRadius: 2,
              border: `1px solid ${theme.palette.primary.main}30`,
              mb: 2,
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: theme.palette.mode === 'dark'
                  ? 'rgba(102, 126, 234, 0.15)'
                  : 'rgba(102, 126, 234, 0.1)',
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}20`
              }
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: theme.palette.primary.main,
                fontSize: '0.75rem',
                fontWeight: 500,
                mb: 1
              }}
            >
              💡 Try Demo Account
            </Typography>
            <Typography
              variant="body2"
              sx={{
                fontSize: '0.7rem',
                color: theme.palette.text.secondary,
                lineHeight: 1.3
              }}
            >
              Click to auto-fill demo credentials
            </Typography>
            <Typography
              variant="body2"
              sx={{
                fontSize: '0.65rem',
                color: theme.palette.text.secondary,
                opacity: 0.7,
                mt: 0.5
              }}
            >
              <EMAIL> • demo123
            </Typography>
          </Box>
        </Box>
      </motion.div>
    </Box>
  )
}

export default LoginForm
