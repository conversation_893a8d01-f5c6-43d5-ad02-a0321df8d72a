import { useState, useCallback } from 'react'
import { useFormik } from 'formik'
import { useAuth } from '../context/AuthContext'
import { useSnackbar } from 'notistack'
import { validateUsername } from '../utils/validation'

export const useAuthForm = (initialValues, validationSchema, onSuccess) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [usernameStatus, setUsernameStatus] = useState({ checking: false, available: null, message: '' })
  const [rememberMe, setRememberMe] = useState(false)

  const { login, register, loading } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  // Debounced username validation
  const checkUsernameAvailability = useCallback(
    debounce(async (username) => {
      if (!username || username.length < 3) {
        setUsernameStatus({ checking: false, available: null, message: '' })
        return
      }

      setUsernameStatus({ checking: true, available: null, message: 'Checking availability...' })
      
      try {
        const result = await validateUsername(username)
        setUsernameStatus({
          checking: false,
          available: result.isValid,
          message: result.message
        })
      } catch (error) {
        setUsernameStatus({
          checking: false,
          available: false,
          message: 'Error checking username availability'
        })
      }
    }, 500),
    []
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting, setFieldError }) => {
      setIsSubmitting(true)
      
      try {
        if (values.username) {
          // Register
          await register({
            name: values.name,
            username: values.username,
            email: values.email,
            password: values.password
          })
        } else {
          // Login
          await login({
            email: values.email,
            password: values.password,
            rememberMe
          })
        }
        
        if (onSuccess) {
          onSuccess()
        }
      } catch (error) {
        const errorMessage = error.response?.data?.message || error.message || 'An error occurred'
        
        // Handle specific field errors
        if (errorMessage.toLowerCase().includes('email')) {
          setFieldError('email', errorMessage)
        } else if (errorMessage.toLowerCase().includes('username')) {
          setFieldError('username', errorMessage)
        } else if (errorMessage.toLowerCase().includes('password')) {
          setFieldError('password', errorMessage)
        } else {
          enqueueSnackbar(errorMessage, { variant: 'error' })
        }
      } finally {
        setIsSubmitting(false)
        setSubmitting(false)
      }
    }
  })

  // Handle username field changes with availability checking
  const handleUsernameChange = (event) => {
    const username = event.target.value
    formik.handleChange(event)
    
    if (username && username.length >= 3) {
      checkUsernameAvailability(username)
    } else {
      setUsernameStatus({ checking: false, available: null, message: '' })
    }
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword)
  }

  const handleRememberMeChange = (event) => {
    setRememberMe(event.target.checked)
  }

  return {
    formik,
    isSubmitting: isSubmitting || loading,
    showPassword,
    showConfirmPassword,
    usernameStatus,
    rememberMe,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    handleUsernameChange,
    handleRememberMeChange
  }
}

// Debounce utility function
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

export default useAuthForm
