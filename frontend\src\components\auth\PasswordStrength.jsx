import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Box, Typography, LinearProgress, Chip, useTheme } from '@mui/material'
import { Check, Close } from '@mui/icons-material'
import { getPasswordStrength } from '../../utils/validation'

const PasswordStrength = ({ password, showDetails = true }) => {
  const theme = useTheme()
  const strength = getPasswordStrength(password)

  if (!password) return null

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Box sx={{ mt: 2 }}>
        {/* Strength Indicator */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" sx={{ mr: 2, minWidth: 100 }}>
            Password Strength:
          </Typography>
          <Box sx={{ flex: 1, mr: 2 }}>
            <LinearProgress
              variant="determinate"
              value={strength.percentage}
              sx={{
                height: 6,
                borderRadius: 3,
                backgroundColor: theme.palette.mode === 'dark' 
                  ? 'rgba(255, 255, 255, 0.1)' 
                  : 'rgba(0, 0, 0, 0.1)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: strength.color,
                  borderRadius: 3,
                  transition: 'all 0.3s ease'
                }
              }}
            />
          </Box>
          <Chip
            label={strength.strength}
            size="small"
            sx={{
              backgroundColor: `${strength.color}20`,
              color: strength.color,
              fontWeight: 600,
              fontSize: '0.75rem'
            }}
          />
        </Box>

        {/* Detailed Requirements */}
        {showDetails && (
          <AnimatePresence>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: 1,
                  mt: 2,
                  p: 2,
                  backgroundColor: theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.03)',
                  borderRadius: 2,
                  border: `1px solid ${theme.palette.divider}`
                }}
              >
                {[
                  { check: password.length >= 8, text: 'At least 8 characters' },
                  { check: /[A-Z]/.test(password), text: 'One uppercase letter' },
                  { check: /[a-z]/.test(password), text: 'One lowercase letter' },
                  { check: /\d/.test(password), text: 'One number' },
                  { check: /[!@#$%^&*(),.?":{}|<>]/.test(password), text: 'One special character' }
                ].map((requirement, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 * index }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1
                      }}
                    >
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ 
                          delay: 0.1 * index,
                          type: "spring",
                          stiffness: 200 
                        }}
                      >
                        {requirement.check ? (
                          <Check
                            sx={{
                              fontSize: 16,
                              color: theme.palette.success.main,
                              backgroundColor: `${theme.palette.success.main}20`,
                              borderRadius: '50%',
                              p: 0.25
                            }}
                          />
                        ) : (
                          <Close
                            sx={{
                              fontSize: 16,
                              color: theme.palette.error.main,
                              backgroundColor: `${theme.palette.error.main}20`,
                              borderRadius: '50%',
                              p: 0.25
                            }}
                          />
                        )}
                      </motion.div>
                      <Typography
                        variant="body2"
                        sx={{
                          color: requirement.check 
                            ? theme.palette.success.main 
                            : theme.palette.text.secondary,
                          fontSize: '0.75rem',
                          fontWeight: requirement.check ? 500 : 400,
                          textDecoration: requirement.check ? 'line-through' : 'none',
                          opacity: requirement.check ? 0.8 : 1
                        }}
                      >
                        {requirement.text}
                      </Typography>
                    </Box>
                  </motion.div>
                ))}
              </Box>
            </motion.div>
          </AnimatePresence>
        )}

        {/* Security Tips */}
        {strength.score < 3 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Box
              sx={{
                mt: 2,
                p: 2,
                backgroundColor: `${theme.palette.warning.main}10`,
                borderRadius: 2,
                border: `1px solid ${theme.palette.warning.main}30`
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.warning.main,
                  fontWeight: 500,
                  mb: 1
                }}
              >
                💡 Security Tips:
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.text.secondary,
                  fontSize: '0.75rem',
                  lineHeight: 1.4
                }}
              >
                Use a mix of uppercase, lowercase, numbers, and symbols. 
                Avoid common words or personal information.
              </Typography>
            </Box>
          </motion.div>
        )}
      </Box>
    </motion.div>
  )
}

export default PasswordStrength
